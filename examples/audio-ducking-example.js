/**
 * Ejemplo de uso del sistema de Audio Ducking
 * 
 * Este ejemplo muestra cómo usar las nuevas funciones de audio ducking
 * para crear una experiencia de audio profesional donde la música de fondo
 * se reduce automáticamente cuando hay narración.
 */

// Importar las funciones necesarias
import { 
  startBackgroundMusic, 
  stopBackgroundMusic, 
  playAudioWithFallback 
} from '../src/utils/audioUtils.js';

import { audioStateManager } from '../src/services/AudioStateManager.js';

/**
 * Ejemplo 1: Configuración básica de música de fondo
 */
async function setupBackgroundMusic() {
  try {
    console.log('🎵 Iniciando música de fondo...');
    
    // Iniciar música de fondo (se reproduce en bucle automáticamente)
    await startBackgroundMusic('/assets/sounds/background-music.mp3');
    
    console.log('✅ Música de fondo iniciada correctamente');
    
    // Suscribirse a cambios de estado para monitorear
    const unsubscribe = audioStateManager.subscribe((state) => {
      console.log('📊 Estado del audio:', {
        narration: state.state,
        backgroundMusic: state.isBackgroundMusicPlaying,
        currentAudio: state.currentAudio ? 'Activo' : 'Inactivo'
      });
    });
    
    return unsubscribe;
    
  } catch (error) {
    console.error('❌ Error iniciando música de fondo:', error);
  }
}

/**
 * Ejemplo 2: Reproducir narración con ducking automático
 */
async function playNarrationWithDucking(audioUrl) {
  try {
    console.log('🎤 Reproduciendo narración con ducking automático...');
    
    await playAudioWithFallback(
      audioUrl,
      // onEnded - Se ejecuta cuando termina la narración
      () => {
        console.log('✅ Narración terminada');
        console.log('🔊 Volumen de música de fondo restaurado automáticamente');
      },
      // onStarted - Se ejecuta cuando inicia la narración
      () => {
        console.log('🎤 Narración iniciada');
        console.log('🔇 Volumen de música de fondo reducido automáticamente');
      },
      // onError - Se ejecuta si hay un error
      (error) => {
        console.error('❌ Error en narración:', error);
        console.log('🔊 Volumen de música de fondo restaurado por error');
      }
    );
    
  } catch (error) {
    console.error('❌ Error reproduciendo narración:', error);
  }
}

/**
 * Ejemplo 3: Secuencia completa de uso
 */
async function completeAudioSequence() {
  console.log('🚀 Iniciando secuencia completa de audio...');
  
  // 1. Configurar música de fondo
  const unsubscribe = await setupBackgroundMusic();
  
  // 2. Esperar un momento para que se establezca la música
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 3. Reproducir varias narraciones con ducking automático
  const narrations = [
    '/assets/sounds/intro.mp3',
    '/assets/sounds/question1.mp3',
    '/assets/sounds/hint1.mp3'
  ];
  
  for (const narrationUrl of narrations) {
    console.log(`\n📢 Reproduciendo: ${narrationUrl}`);
    await playNarrationWithDucking(narrationUrl);
    
    // Pausa entre narraciones para demostrar la restauración del volumen
    console.log('⏸️ Pausa entre narraciones...');
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // 4. Detener música de fondo al final
  console.log('\n🛑 Deteniendo música de fondo...');
  stopBackgroundMusic();
  
  // 5. Limpiar suscripción
  if (unsubscribe) {
    unsubscribe();
  }
  
  console.log('✅ Secuencia completa terminada');
}

/**
 * Ejemplo 4: Manejo de múltiples narraciones rápidas
 */
async function handleRapidNarrations() {
  console.log('⚡ Probando narraciones rápidas...');
  
  // Configurar música de fondo
  await setupBackgroundMusic();
  
  // Reproducir narraciones rápidamente (el sistema debe manejar esto correctamente)
  const rapidNarrations = [
    '/assets/sounds/short1.mp3',
    '/assets/sounds/short2.mp3',
    '/assets/sounds/short3.mp3'
  ];
  
  // Iniciar todas las narraciones rápidamente
  // El AudioStateManager debe manejar esto sin problemas
  const promises = rapidNarrations.map(url => 
    playNarrationWithDucking(url)
  );
  
  try {
    await Promise.all(promises);
    console.log('✅ Todas las narraciones rápidas completadas');
  } catch (error) {
    console.error('❌ Error en narraciones rápidas:', error);
  }
  
  // Limpiar
  stopBackgroundMusic();
}

/**
 * Ejemplo 5: Configuración personalizada de volúmenes
 */
function customVolumeConfiguration() {
  console.log('🎛️ Configuración personalizada de volúmenes');
  
  // Nota: Para personalizar los volúmenes, modifica estas propiedades en AudioStateManager:
  console.log(`
  Para personalizar los volúmenes, edita src/services/AudioStateManager.ts:
  
  // Volumen normal de música de fondo (por defecto: 0.3 = 30%)
  private originalBackgroundVolume = 0.4; // 40%
  
  // Volumen reducido durante narración (por defecto: 0.1 = 10%)
  private duckedBackgroundVolume = 0.05; // 5%
  
  // Duración de transiciones de volumen (por defecto: 500ms)
  private volumeTransitionDuration = 1000; // 1 segundo
  `);
}

/**
 * Ejemplo 6: Monitoreo avanzado del estado
 */
function advancedStateMonitoring() {
  console.log('📊 Configurando monitoreo avanzado...');
  
  const unsubscribe = audioStateManager.subscribe((state) => {
    const timestamp = new Date().toLocaleTimeString();
    
    console.log(`[${timestamp}] Estado del Audio:`, {
      // Estado de la narración actual
      narrationState: state.state,
      
      // Si hay música de fondo reproduciéndose
      backgroundMusicActive: state.isBackgroundMusicPlaying,
      
      // Si hay audio de narración actual
      hasCurrentAudio: !!state.currentAudio,
      
      // Duración del audio actual (si existe)
      currentDuration: state.duration || 0,
      
      // Tiempo transcurrido del audio actual
      currentTime: state.currentTime || 0,
      
      // Progreso en porcentaje
      progress: state.duration > 0 ? 
        Math.round((state.currentTime / state.duration) * 100) : 0
    });
  });
  
  // Devolver función para limpiar la suscripción
  return unsubscribe;
}

// Exportar ejemplos para uso
export {
  setupBackgroundMusic,
  playNarrationWithDucking,
  completeAudioSequence,
  handleRapidNarrations,
  customVolumeConfiguration,
  advancedStateMonitoring
};

// Ejemplo de uso directo (descomenta para probar)
/*
// Ejecutar secuencia completa
completeAudioSequence().catch(console.error);

// O probar narraciones rápidas
// handleRapidNarrations().catch(console.error);

// O configurar monitoreo avanzado
// const cleanup = advancedStateMonitoring();
// setTimeout(cleanup, 30000); // Limpiar después de 30 segundos
*/
