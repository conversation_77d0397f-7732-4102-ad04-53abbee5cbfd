# Implementación de Audio Ducking

## Resumen

Se ha implementado exitosamente un sistema de **audio ducking** que permite que la música de fondo se reduzca automáticamente cuando hay narración (IA o usuario), creando una experiencia de audio más profesional y clara.

## ✅ Funcionalidades Implementadas

### 1. **Música de Fondo Continua**
- La música se reproduce en bucle automático
- Volumen configurable (por defecto: 30%)
- Inicia automáticamente al aceptar cookies
- Se mantiene activa durante toda la sesión

### 2. **Ducking Automático**
- **Reducción automática**: Cuando inicia narración → volumen baja a 10%
- **Restauración automática**: Cuando termina narración → volumen vuelve a 30%
- **Transiciones suaves**: Cambios graduales de volumen en 500ms
- **Sin interrupciones**: La música nunca se detiene, solo cambia de volumen

### 3. **Gestión Centralizada**
- Todo el audio se maneja desde `AudioStateManager`
- Prevención de condiciones de carrera
- Estado consistente entre componentes
- Limpieza automática de recursos

## 🔧 Archivos Modificados

### Servicios Principales
- **`src/services/AudioStateManager.ts`**: Sistema central de gestión de audio
  - Soporte para música de fondo
  - Funciones de ducking automático
  - Transiciones suaves de volumen
  - Gestión de estado mejorada

### Utilidades
- **`src/utils/audioUtils.ts`**: Funciones de conveniencia
  - `startBackgroundMusic()`: Iniciar música de fondo
  - `stopBackgroundMusic()`: Detener música de fondo
  - `playAudioWithFallback()`: Reproducir narración (con ducking automático)

### Componentes
- **`src/components/CookieBanner.tsx`**: Inicia música de fondo al aceptar cookies
- **`src/components/AudioDuckingDemo.tsx`**: Componente de demostración

### Documentación
- **`docs/audio-ducking-guide.md`**: Guía completa de uso
- **`examples/audio-ducking-example.js`**: Ejemplos de implementación

## 🎛️ Configuración

### Volúmenes por Defecto
```typescript
private originalBackgroundVolume = 0.3; // 30% - Volumen normal
private duckedBackgroundVolume = 0.1;   // 10% - Volumen reducido
private volumeTransitionDuration = 500; // 500ms - Duración de transiciones
```

### Personalización
Para cambiar los volúmenes, edita las propiedades en `AudioStateManager.ts`:

```typescript
// Música más alta
private originalBackgroundVolume = 0.4; // 40%

// Reducción más agresiva
private duckedBackgroundVolume = 0.05;  // 5%

// Transiciones más lentas
private volumeTransitionDuration = 1000; // 1 segundo
```

## 🚀 Uso Básico

### 1. Iniciar Música de Fondo
```typescript
import { startBackgroundMusic } from '../utils/audioUtils';

await startBackgroundMusic('/assets/sounds/background-music.mp3');
```

### 2. Reproducir Narración (Ducking Automático)
```typescript
import { playAudioWithFallback } from '../utils/audioUtils';

await playAudioWithFallback(
  '/path/to/narration.mp3',
  () => console.log('Narración terminada - música restaurada'),
  () => console.log('Narración iniciada - música reducida'),
  (error) => console.error('Error:', error)
);
```

### 3. Monitorear Estado
```typescript
import { audioStateManager } from '../services/AudioStateManager';

const unsubscribe = audioStateManager.subscribe((state) => {
  console.log('Estado narración:', state.state);
  console.log('Música de fondo activa:', state.isBackgroundMusicPlaying);
});
```

## 🔄 Flujo de Funcionamiento

1. **Usuario acepta cookies** → Música de fondo inicia (30% volumen)
2. **IA genera respuesta** → Audio de narración se reproduce
3. **Narración inicia** → Música se reduce a 10% (transición suave 500ms)
4. **Narración activa** → Música continúa a volumen reducido
5. **Narración termina** → Música vuelve a 30% (transición suave 500ms)
6. **Ciclo continúa** → Música sigue reproduciéndose hasta cerrar aplicación

## ✨ Beneficios

### Para el Usuario
- **Experiencia profesional**: Audio balanceado y claro
- **Sin interrupciones**: Música continua sin cortes abruptos
- **Claridad de voz**: Narración siempre audible por encima de la música
- **Transiciones suaves**: Cambios de volumen graduales y naturales

### Para el Desarrollador
- **Automático**: No requiere gestión manual del volumen
- **Robusto**: Maneja condiciones de carrera y errores
- **Configurable**: Volúmenes y tiempos personalizables
- **Monitoreado**: Estado observable en tiempo real

## 🧪 Testing

Los archivos de test han sido actualizados para soportar las nuevas funciones async:
- `src/tests/AudioStateManager.test.ts`
- `src/tests/ConversationFlow.integration.test.ts`
- `src/tests/runTimingTests.ts`

## 📱 Compatibilidad

- ✅ **Navegadores modernos**: Chrome, Firefox, Safari, Edge
- ✅ **Dispositivos móviles**: iOS Safari, Android Chrome
- ✅ **Políticas de autoplay**: Requiere interacción del usuario (botón de cookies)
- ✅ **Web Audio API**: Funciona en todos los navegadores compatibles

## 🔍 Solución de Problemas

### La música no inicia
- Verificar interacción del usuario (botón de cookies presionado)
- Comprobar que el archivo de audio existe
- Revisar consola para errores de autoplay

### El ducking no funciona
- Verificar que la música de fondo esté reproduciéndose
- Comprobar que la narración use `playAudioWithFallback()`
- Revisar logs de consola para errores

### Volumen incorrecto
- Verificar configuración en `AudioStateManager.ts`
- Comprobar que no haya otros controles de volumen

## 🎯 Próximos Pasos

El sistema está completamente funcional y listo para producción. Posibles mejoras futuras:

1. **Configuración por usuario**: Permitir ajustar volúmenes desde UI
2. **Múltiples pistas**: Soporte para diferentes músicas de fondo
3. **Efectos de audio**: Fade in/out más sofisticados
4. **Análisis de audio**: Ducking basado en frecuencias de voz

---

**Estado**: ✅ **COMPLETADO Y FUNCIONAL**  
**Fecha**: 2025-09-04  
**Versión**: 1.0.0
