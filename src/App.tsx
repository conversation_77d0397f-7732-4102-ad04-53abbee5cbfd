// React core
import { useState, useEffect, useCallback } from "react";
// Third-party library imports
// Services
import { AppService } from "./services/AppService";
import { ConversationStorage } from "./services/ConversationStorage";
// Components
import { SimpleVoiceChat } from "./components/SimpleVoiceChat";
import { CookieBanner } from "./components/CookieBanner";
import { RulesPopup } from "./components/RulesPopup";
import { GameEndScreen } from "./components/GameEndScreen";
// Utils & Constants & Helpers
import { GAME_MESSAGES } from "./utils/gameUtils";
import { playAudioWithFallback } from "./utils/audioUtils";
// Styles
import "./App.css";

// Game Steps Constants
const GameStep = {
  COOKIE_BANNER: 'cookie_banner',
  MAIN_MENU: 'main_menu',
  GAME_PLAYING: 'game_playing',
  GAME_END: 'game_end'
} as const;

type GameStepType = typeof GameStep[keyof typeof GameStep];

function App() {
  // Step management
  const [currentStep, setCurrentStep] = useState<GameStepType>(GameStep.COOKIE_BANNER);
  const [showRulesPopup, setShowRulesPopup] = useState<boolean>(false);

  // Game state management
  const [aiLoading, setAiLoading] = useState<boolean>(false);
  const [generatedCharacter, setGeneratedCharacter] = useState<string>("");
  const [gameStarted, setGameStarted] = useState<boolean>(false);
  const [initialMessage, setInitialMessage] = useState<string>("");
  const [gameWon, setGameWon] = useState<boolean>(false);
  const [, setCharacterError] = useState<string>("");

  // Service instances (singletons)
  const appService = AppService.getInstance();
  const conversationStorage = ConversationStorage.getInstance();

  /**
   * Initialize app by clearing previous conversations
   * This ensures a fresh start for each session
   */
  useEffect(() => {
    conversationStorage.clearAllConversations();
    // console.log("🧹 Conversaciones limpiadas al inicializar la aplicación");
  }, [conversationStorage]);

  /**
   * Configure audio playback callback
   * Sets up automatic audio playback with fallback handling
   */
  useEffect(() => {
    appService.setAudioCallback((audioUrl: string) => {
      // Small delay to ensure proper audio context initialization
      setTimeout(() => {
        const audioFinishedCallback = appService.getAudioFinishedCallback();
        const audioStartedCallback = appService.getAudioStartedCallback();

        playAudioWithFallback(
          audioUrl,
          audioFinishedCallback, // onEnded
          audioStartedCallback, // onStarted
          (error: string) => {
            // onError
            console.error("❌ Error reproduciendo audio:", error);
            // Ejecutar callback de finalización en caso de error
            if (audioFinishedCallback) {
              setTimeout(() => {
                audioFinishedCallback();
              }, 1000);
            }
          }
        );
      }, 100);
    });
  }, [appService]);

  /**
   * Extract response text from API response object
   * Handles multiple possible response field names
   */
  const extractResponseText = useCallback(
    (response: any, fallback = "Respuesta no encontrada") => {
      return (
        response.response ||
        response.output ||
        response.result ||
        response.text ||
        response.content ||
        fallback
      );
    },
    []
  );

  /**
   * Step navigation functions
   */
  const handleAcceptCookies = useCallback(() => {
    setCurrentStep(GameStep.MAIN_MENU);
  }, []);

  const handleShowRules = useCallback(() => {
    setShowRulesPopup(true);
  }, []);

  const handleCloseRules = useCallback(() => {
    setShowRulesPopup(false);
  }, []);



  /**
   * Main game initialization function
   * Combines character generation and game start in a single flow
   */
  const handleStartGameDirectly = useCallback(async () => {
    setAiLoading(true);
    setCharacterError("");

    try {
      // Step 1: Generate character
      const characterResponse = await appService.generateWithGenCharBot(
        GAME_MESSAGES.GENERATE_CHARACTER
      );

      const characterText = extractResponseText(characterResponse);
      if (!characterText || characterText === "Respuesta no encontrada") {
        throw new Error("No se pudo generar el personaje");
      }

      setGeneratedCharacter(characterText);

      // Step 2: Start game immediately
      setGameStarted(true);

      const gameResponse = await appService.generateWithIaVsPlayer(
        GAME_MESSAGES.INITIAL_GAME_QUERY,
        characterText
      );

      const responseText = extractResponseText(gameResponse);
      setInitialMessage(responseText);
    } catch (error) {
      console.error("❌ Error en inicio directo del juego:", error);
      setCharacterError(
        "Error al generar personaje o iniciar juego. Inténtalo de nuevo."
      );
      setGameStarted(false);
      setGeneratedCharacter("");
    } finally {
      setAiLoading(false);
    }
  }, [appService, extractResponseText]);

  /**
   * Handle start game from main menu
   * Transitions to game playing step and starts the game
   */
  const handleStartGame = useCallback(() => {
    setCurrentStep(GameStep.GAME_PLAYING);
    handleStartGameDirectly();
  }, [handleStartGameDirectly]);

  /**
   * Handle game end
   * Transitions to game end step and stores the result
   */
  const handleGameEnd = useCallback((won: boolean) => {
    setGameWon(won);
    setCurrentStep(GameStep.GAME_END);
  }, []);

  /**
   * Reset game to initial state
   * Clears all game data and conversation history
   */
  const handleResetGame = useCallback(() => {
    setCurrentStep(GameStep.MAIN_MENU);
    setGeneratedCharacter("");
    setGameStarted(false);
    setInitialMessage("");
    setGameWon(false);
    setCharacterError("");
    setShowRulesPopup(false);
    conversationStorage.clearAllConversations();
    // console.log("🔄 Juego reiniciado y conversaciones limpiadas");
  }, [conversationStorage]);

  /**
   * Render step content based on current step
   */
  const renderStepContent = () => {
    switch (currentStep) {
      case GameStep.COOKIE_BANNER:
        return <CookieBanner onAccept={handleAcceptCookies} />;

      case GameStep.MAIN_MENU:
        return (
          <div className="card">
            <div className="game-container">
              <h3 className="game-title">🎯 Juego de Adivinanza de Personajes</h3>

              {/* Main Menu Section */}
              <div className="quick-start-section">
                <h4 className="quick-start-title">🚀 ¡Bienvenido al Desafío!</h4>
                <p className="quick-start-description">
                  ¿Estás listo para poner a prueba tu ingenio? Se generará un personaje misterioso
                  y tendrás que adivinarlo haciendo preguntas inteligentes. ¡Usa tu voz para interactuar
                  con la IA y descubre quién se esconde detrás del misterio!
                </p>

                <div className="buttons-container" style={{ display: 'flex', gap: '12px', flexWrap: 'wrap', justifyContent: 'center' }}>
                  <button
                    onClick={handleStartGame}
                    disabled={aiLoading}
                    className="primary-button"
                    style={{ flex: '1', minWidth: '200px' }}
                  >
                    {aiLoading ? "Iniciando Juego..." : "🎮 INICIAR JUEGO"}
                  </button>

                  <button
                    onClick={handleShowRules}
                    className="secondary-button"
                    style={{
                      backgroundColor: '#6c757d',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '12px 24px',
                      fontSize: '16px',
                      fontWeight: '600',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      flex: '0 0 auto'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#5a6268';
                      e.currentTarget.style.transform = 'translateY(-1px)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = '#6c757d';
                      e.currentTarget.style.transform = 'translateY(0)';
                    }}
                  >
                    📋 Ver Reglas
                  </button>
                </div>

                {/* Temporary test buttons - Remove in production */}
                <div style={{ marginTop: '20px', textAlign: 'center' }}>
                  <small style={{ color: '#666', display: 'block', marginBottom: '10px' }}>
                    🧪 Botones de prueba (remover en producción):
                  </small>
                  <div style={{ display: 'flex', gap: '8px', justifyContent: 'center', flexWrap: 'wrap' }}>
                    <button
                      onClick={() => handleGameEnd(true)}
                      style={{
                        backgroundColor: '#28a745',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        padding: '6px 12px',
                        fontSize: '12px',
                        cursor: 'pointer'
                      }}
                    >
                      🎉 Simular Victoria
                    </button>
                    <button
                      onClick={() => handleGameEnd(false)}
                      style={{
                        backgroundColor: '#dc3545',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        padding: '6px 12px',
                        fontSize: '12px',
                        cursor: 'pointer'
                      }}
                    >
                      😔 Simular Derrota
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case GameStep.GAME_PLAYING:
        return (
          <div className="card">
            <div className="game-container">
              <h3 className="game-title">🎯 Juego de Adivinanza de Personajes</h3>

              {/* Voice Chat Component - handles all conversation history */}
              <SimpleVoiceChat
                generatedCharacter={generatedCharacter}
                isGameStarted={gameStarted}
                initialMessage={initialMessage}
                onGameEnd={handleGameEnd}
              />

              {/* Reset Game Section */}
              {(generatedCharacter || gameStarted) && (
                <div className="reset-section">
                  <button onClick={handleResetGame} className="reset-button">
                    🔄 Reiniciar Juego
                  </button>
                </div>
              )}
            </div>
          </div>
        );

      case GameStep.GAME_END:
        return (
          <GameEndScreen
            gameWon={gameWon}
            onClose={handleResetGame}
            onNewGame={handleResetGame}
          />
        );

      default:
        return null;
    }
  };

  return (
    <>
      {renderStepContent()}

      {/* Rules Popup */}
      <RulesPopup
        isOpen={showRulesPopup}
        onClose={handleCloseRules}
      />
    </>
  );
}

export default App;
