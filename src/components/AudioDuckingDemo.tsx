import React, { useState, useEffect } from 'react';
import { startBackgroundMusic, stopBackgroundMusic, playAudioWithFallback } from '../utils/audioUtils';
import { audioStateManager } from '../services/AudioStateManager';
import type { AudioStateData } from '../services/AudioStateManager';

/**
 * Demo component to showcase audio ducking functionality
 * This component demonstrates how background music automatically reduces volume
 * when narration plays and restores when narration ends
 */
export const AudioDuckingDemo: React.FC = () => {
  const [audioState, setAudioState] = useState<AudioStateData>(audioStateManager.getState());
  const [isBackgroundMusicStarted, setIsBackgroundMusicStarted] = useState(false);

  // Subscribe to audio state changes
  useEffect(() => {
    const unsubscribe = audioStateManager.subscribe((state) => {
      setAudioState(state);
    });

    return unsubscribe;
  }, []);

  const handleStartBackgroundMusic = async () => {
    try {
      await startBackgroundMusic('/assets/sounds/sound.mp3');
      setIsBackgroundMusicStarted(true);
      console.log('🎵 Background music started');
    } catch (error) {
      console.error('Error starting background music:', error);
    }
  };

  const handleStopBackgroundMusic = () => {
    stopBackgroundMusic();
    setIsBackgroundMusicStarted(false);
    console.log('🛑 Background music stopped');
  };

  const handlePlayNarration = async () => {
    try {
      // This would normally be a TTS-generated audio URL
      // For demo purposes, we'll use a placeholder
      const demoAudioUrl = '/assets/sounds/demo-narration.mp3';

      await playAudioWithFallback(
        demoAudioUrl,
        () => {
          console.log('🎤 Narration ended - background music volume restored');
        },
        () => {
          console.log('🎤 Narration started - background music volume ducked');
        },
        (error) => {
          console.error('Error playing narration:', error);
        }
      );
    } catch (error) {
      console.error('Error playing narration:', error);
    }
  };

  return (
    <div style={{
      padding: '20px',
      backgroundColor: '#f8f9fa',
      borderRadius: '8px',
      margin: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h3 style={{ color: '#495057', marginBottom: '20px' }}>
        🎵 Audio Ducking Demo
      </h3>

      <div style={{ marginBottom: '20px' }}>
        <p style={{ color: '#6c757d', fontSize: '14px', lineHeight: '1.5' }}>
          Este demo muestra cómo la música de fondo se reduce automáticamente cuando hay narración.
          La música vuelve a su volumen original cuando termina la narración.
        </p>
      </div>

      <div style={{ display: 'flex', gap: '10px', marginBottom: '20px', flexWrap: 'wrap' }}>
        <button
          onClick={handleStartBackgroundMusic}
          disabled={isBackgroundMusicStarted}
          style={{
            padding: '10px 16px',
            backgroundColor: isBackgroundMusicStarted ? '#6c757d' : '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: isBackgroundMusicStarted ? 'not-allowed' : 'pointer',
            fontSize: '14px'
          }}
        >
          🎵 Iniciar Música de Fondo
        </button>

        <button
          onClick={handleStopBackgroundMusic}
          disabled={!isBackgroundMusicStarted}
          style={{
            padding: '10px 16px',
            backgroundColor: !isBackgroundMusicStarted ? '#6c757d' : '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: !isBackgroundMusicStarted ? 'not-allowed' : 'pointer',
            fontSize: '14px'
          }}
        >
          🛑 Detener Música de Fondo
        </button>

        <button
          onClick={handlePlayNarration}
          disabled={!isBackgroundMusicStarted || audioState.state === 'playing'}
          style={{
            padding: '10px 16px',
            backgroundColor: (!isBackgroundMusicStarted || audioState.state === 'playing') ? '#6c757d' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: (!isBackgroundMusicStarted || audioState.state === 'playing') ? 'not-allowed' : 'pointer',
            fontSize: '14px'
          }}
        >
          🎤 Reproducir Narración (Demo)
        </button>
      </div>

      <div style={{
        padding: '15px',
        backgroundColor: 'white',
        borderRadius: '5px',
        border: '1px solid #dee2e6'
      }}>
        <h4 style={{ color: '#495057', marginBottom: '10px', fontSize: '16px' }}>
          Estado del Audio:
        </h4>
        <div style={{ fontSize: '14px', color: '#6c757d' }}>
          <p><strong>Estado de narración:</strong> {audioState.state}</p>
          <p><strong>Música de fondo activa:</strong> {audioState.isBackgroundMusicPlaying ? '✅ Sí' : '❌ No'}</p>
          <p><strong>Audio actual:</strong> {audioState.currentAudio ? 'Reproduciendo' : 'Ninguno'}</p>
        </div>
      </div>

      <div style={{
        marginTop: '15px',
        padding: '10px',
        backgroundColor: '#e7f3ff',
        borderRadius: '5px',
        fontSize: '12px',
        color: '#0066cc'
      }}>
        <strong>💡 Cómo funciona:</strong>
        <ul style={{ marginTop: '5px', paddingLeft: '20px' }}>
          <li>La música de fondo se reproduce en bucle a volumen normal (30%)</li>
          <li>Cuando inicia la narración, la música se reduce suavemente a 10%</li>
          <li>Cuando termina la narración, la música vuelve a 30%</li>
          <li>Las transiciones de volumen son suaves (500ms)</li>
        </ul>
      </div>
    </div>
  );
};
