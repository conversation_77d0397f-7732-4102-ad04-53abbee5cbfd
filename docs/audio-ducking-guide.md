# Guía de Audio Ducking

## Descripción

El sistema de audio ducking permite que la música de fondo se reduzca automáticamente cuando hay narración (ya sea de la IA o del usuario), creando una experiencia de audio más profesional y clara.

## Características

- **Música de fondo continua**: La música se reproduce en bucle sin interrupciones
- **Ducking automático**: El volumen se reduce cuando inicia la narración
- **Restauración automática**: El volumen se restaura cuando termina la narración
- **Transiciones suaves**: Los cambios de volumen son graduales (500ms)
- **Gestión centralizada**: Todo el audio se maneja desde `AudioStateManager`

## Configuración de Volúmenes

```typescript
// Volúmenes por defecto en AudioStateManager
private originalBackgroundVolume = 0.3; // 30% - Volumen normal de música de fondo
private duckedBackgroundVolume = 0.1;   // 10% - Volumen reducido durante narración
private volumeTransitionDuration = 500; // 500ms - Duración de transiciones
```

## Uso Básico

### 1. Iniciar Música de Fondo

```typescript
import { startBackgroundMusic } from '../utils/audioUtils';

// Iniciar música de fondo (se reproduce en bucle)
await startBackgroundMusic('/assets/sounds/background-music.mp3');
```

### 2. Reproducir Narración con Ducking Automático

```typescript
import { playAudioWithFallback } from '../utils/audioUtils';

// La música de fondo se reducirá automáticamente
await playAudioWithFallback(
  '/path/to/narration.mp3',
  () => {
    console.log('Narración terminada - música restaurada');
  },
  () => {
    console.log('Narración iniciada - música reducida');
  },
  (error) => {
    console.error('Error en narración:', error);
  }
);
```

### 3. Detener Música de Fondo

```typescript
import { stopBackgroundMusic } from '../utils/audioUtils';

// Detener música de fondo
stopBackgroundMusic();
```

## Flujo de Funcionamiento

1. **Inicio**: La música de fondo se reproduce a volumen normal (30%)
2. **Narración inicia**: El volumen se reduce suavemente a 10% en 500ms
3. **Narración activa**: La música continúa a volumen reducido
4. **Narración termina**: El volumen se restaura suavemente a 30% en 500ms
5. **Ciclo continúa**: La música sigue reproduciéndose hasta que se detenga manualmente

## Integración en la Aplicación

### En CookieBanner.tsx

```typescript
const handleAcceptAndPlay = async () => {
  try {
    // Iniciar música de fondo al aceptar cookies
    await startBackgroundMusic('/assets/sounds/sound.mp3');
    console.log('🎵 Música de fondo iniciada');
  } catch (error) {
    console.warn('⚠️ Error reproduciendo música de fondo:', error);
  }
  onAccept();
};
```

### En el Sistema de Conversación

El ducking funciona automáticamente cuando se reproduce cualquier audio de narración a través de `playAudioWithFallback()`. No se requiere código adicional.

## Monitoreo del Estado

```typescript
import { audioStateManager } from '../services/AudioStateManager';

// Suscribirse a cambios de estado
const unsubscribe = audioStateManager.subscribe((state) => {
  console.log('Estado de narración:', state.state);
  console.log('Música de fondo activa:', state.isBackgroundMusicPlaying);
  console.log('Audio actual:', state.currentAudio);
});

// Limpiar suscripción
unsubscribe();
```

## Personalización

### Ajustar Volúmenes

Para cambiar los volúmenes por defecto, modifica las propiedades en `AudioStateManager`:

```typescript
// En src/services/AudioStateManager.ts
private originalBackgroundVolume = 0.4; // Música más alta
private duckedBackgroundVolume = 0.05;  // Reducción más agresiva
```

### Ajustar Duración de Transiciones

```typescript
// En src/services/AudioStateManager.ts
private volumeTransitionDuration = 1000; // Transiciones más lentas (1 segundo)
```

## Consideraciones Técnicas

- **Compatibilidad**: Funciona en todos los navegadores modernos que soporten Web Audio API
- **Rendimiento**: Las transiciones de volumen son eficientes y no afectan el rendimiento
- **Memoria**: La música de fondo se mantiene en memoria mientras esté activa
- **Autoplay**: Requiere interacción del usuario para iniciar (política de autoplay de navegadores)

## Solución de Problemas

### La música no se inicia
- Verificar que el usuario haya interactuado con la página
- Comprobar que el archivo de audio existe y es accesible
- Revisar la consola para errores de autoplay

### El ducking no funciona
- Verificar que la música de fondo esté reproduciéndose
- Comprobar que la narración se reproduzca a través de `playAudioWithFallback()`
- Revisar los logs de consola para errores

### Volumen incorrecto
- Verificar las configuraciones de volumen en `AudioStateManager`
- Comprobar que no haya otros controles de volumen interfiriendo
