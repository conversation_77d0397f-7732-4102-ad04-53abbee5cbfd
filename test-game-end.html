<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Game End Transition</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .test-btn {
            background-color: #007bff;
            color: white;
        }
        .test-btn:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Game End Transition</h1>
    
    <div class="test-section">
        <h2>Test JSON Response Parsing</h2>
        <p>This test simulates the JSON response that should trigger the game end transition.</p>
        
        <button class="test-btn" onclick="testJsonParsing()">Test JSON Parsing</button>
        <div id="json-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>Test Game Progress Update</h2>
        <p>This test simulates updating the game progress with game finished state.</p>
        
        <button class="test-btn" onclick="testGameProgressUpdate()">Test Game Progress Update</button>
        <div id="progress-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>Instructions for Manual Testing</h2>
        <ol>
            <li>Open the main application in another tab: <a href="http://localhost:5174/" target="_blank">http://localhost:5174/</a></li>
            <li>Start a new game</li>
            <li>When the game starts, open the browser console (F12)</li>
            <li>Look for the following debug messages when the game ends:
                <ul>
                    <li><code>🔍 Parsed JSON from response:</code> - Should show the parsed JSON</li>
                    <li><code>🎮 Updating game progress:</code> - Should show gameFinished: true</li>
                    <li><code>🎮 Progreso del juego actualizado:</code> - Should show updated progress</li>
                    <li><code>🎮 Game finished detected:</code> - Should trigger when game ends</li>
                    <li><code>🎯 Game ended, transitioning to end screen:</code> - Should trigger transition</li>
                </ul>
            </li>
            <li>The game should automatically transition to the win/lose screen</li>
        </ol>
    </div>

    <script>
        function testJsonParsing() {
            const testResponse = `{
  "respuesta": "¡Correcto! Has adivinado que era Gandalf.",
  "acertado": true,
  "juego_finalizado": true
}

¿Quieres jugar otra vez? Puedo pensar en otro personaje si quieres.`;

            const resultDiv = document.getElementById('json-result');
            resultDiv.style.display = 'block';

            try {
                // Simulate the JSON parsing logic from AppService
                let jsonStr = testResponse.trim();
                
                if (jsonStr.startsWith("{")) {
                    let braceCount = 0;
                    let jsonEndIndex = -1;
                    
                    for (let i = 0; i < jsonStr.length; i++) {
                        if (jsonStr[i] === '{') {
                            braceCount++;
                        } else if (jsonStr[i] === '}') {
                            braceCount--;
                            if (braceCount === 0) {
                                jsonEndIndex = i;
                                break;
                            }
                        }
                    }
                    
                    if (jsonEndIndex !== -1) {
                        jsonStr = jsonStr.substring(0, jsonEndIndex + 1);
                    }
                }
                
                const parsed = JSON.parse(jsonStr);
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ JSON Parsing Successful!</strong><br>
                    <pre>${JSON.stringify(parsed, null, 2)}</pre>
                    <p><strong>Game Finished:</strong> ${parsed.juego_finalizado}</p>
                    <p><strong>Game Won:</strong> ${parsed.acertado}</p>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ JSON Parsing Failed!</strong><br>
                    <p>Error: ${error.message}</p>
                `;
            }
        }

        function testGameProgressUpdate() {
            const resultDiv = document.getElementById('progress-result');
            resultDiv.style.display = 'block';

            // Simulate the game progress update
            const mockProgress = {
                questionsAsked: 12,
                questionsRemaining: 0,
                hints: [],
                gameFinished: true,
                gameWon: true
            };

            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                <strong>✅ Game Progress Update Simulation</strong><br>
                <pre>${JSON.stringify(mockProgress, null, 2)}</pre>
                <p>This progress should trigger the game end transition.</p>
            `;
        }
    </script>
</body>
</html>
